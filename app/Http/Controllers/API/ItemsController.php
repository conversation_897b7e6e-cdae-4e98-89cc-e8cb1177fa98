<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\ServiceCategoryItemResource;
use App\Models\Admin\City;
use App\Models\Admin\ServiceCategory;
use App\Models\Admin\ServiceCategoryItem;
use App\Models\Admin\ServiceCategoryItemSearch;
use Illuminate\Http\Request;

class ItemsController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Get items list.
     *
     * @return \Illuminate\Http\Response
     */
    public function get_list(Request $request)
    {
        $request->validate([
            'service_category_id' => 'required|integer|exists:service_categories,id',
            'reels'               => 'nullable|boolean',
            'has_video'           => 'nullable|boolean',
            'city_id'             => 'nullable|integer|exists:cities,id',
            'category_id'         => 'nullable|integer|exists:service_categories,id',
            'property_type_id'    => 'nullable|integer|exists:property_types,id',
            'page'                => 'nullable|integer',
            'limit'               => 'nullable|integer',
            'sort'                => 'nullable|string|in:asc,desc',
            'sort_by'             => 'nullable|string|in:created_at,updated_at,title',
        ]);

        $category   = ServiceCategory::find($request->service_category_id);
        $categories = [];
        if (! $category->parent) {
            $categories = $category->childrenCategories->pluck('id')->toArray() ?? [];
            array_push($categories, $category->id);
        } else {
            array_push($categories, $category->id);
        }



        // Build the base query
        $query = ServiceCategoryItem::whereIn('service_category_id', $categories)
            ->where('active', 1);

        // Apply video filter for reels
        if ($request->has('reels') && $request->reels) {
            $query->whereNotNull('video');
        }

        // Apply has_video filter
        if ($request->has('has_video') && $request->has_video) {
            $query->whereNotNull('video');
        }

        // Apply city filter
        if ($request->has('city_id') && $request->city_id) {
            $query->where('city_id', $request->city_id);
        }

        // Apply category filter (subcategory filter)
        if ($request->has('category_id') && $request->category_id) {
            $query->where('service_category_id', $request->category_id);
        }

        // Apply property type filter
        if ($request->has('property_type_id') && $request->property_type_id) {
            $query->where('property_type_id', $request->property_type_id);
        }

        // Apply sorting and pagination
        $list = $query->orderBy($request->sort_by ?? 'created_at', $request->sort ?? 'desc')
            ->paginate($request->limit ?? 10);



        // Increment views for all items in a single query
        if ($list->isNotEmpty()) {
            ServiceCategoryItem::whereIn('id', $list->pluck('id'))->increment('views');
        }

        // Return paginated json response
        return response()->json([
            'success' => !empty($list->items()),
            'message' => trans('api.' . (!empty($list->items()) ? 'data_retrieved_success' : 'no_data_found')),
            'data' => [
                'items' => ServiceCategoryItemResource::collection($list->items()),
                'pagination' => [
                    'current_page' => $list->currentPage(),
                    'last_page' => $list->lastPage(),
                    'per_page' => $list->perPage(),
                    'total' => $list->total(),
                    'has_more' => $list->hasMorePages(),
                ]
            ],
            // Add debug info when no items found
            'debug_info' => empty($list->items()) ? [
                'applied_filters' => [
                    'service_category_id' => $request->service_category_id,
                    'city_id' => $request->city_id,
                    'category_id' => $request->category_id,
                    'property_type_id' => $request->property_type_id,
                    'reels' => $request->reels,
                    'has_video' => $request->has_video,
                ],
                'categories_searched' => $categories,
                'total_items_in_category' => ServiceCategoryItem::whereIn('service_category_id', $categories)->count(),
                'active_items_in_category' => ServiceCategoryItem::whereIn('service_category_id', $categories)->where('active', 1)->count(),
            ] : null
        ], !empty($list->items()) ? 200 : 404);
    }

    /**
     * Debug endpoint to check available data
     *
     * @return \Illuminate\Http\Response
     */
    public function debug_data(Request $request)
    {
        $serviceCategoryId = $request->service_category_id ?? 1;

        // Get category info
        $category = ServiceCategory::find($serviceCategoryId);
        $categories = [];
        if ($category) {
            if (!$category->parent) {
                $categories = $category->childrenCategories->pluck('id')->toArray() ?? [];
                array_push($categories, $category->id);
            } else {
                array_push($categories, $category->id);
            }
        }

        // Count items by different criteria
        $totalItems = ServiceCategoryItem::whereIn('service_category_id', $categories)->count();
        $activeItems = ServiceCategoryItem::whereIn('service_category_id', $categories)->where('active', 1)->count();
        $itemsWithVideo = ServiceCategoryItem::whereIn('service_category_id', $categories)->where('active', 1)->whereNotNull('video')->count();

        // Get sample items with their properties
        $sampleItems = ServiceCategoryItem::whereIn('service_category_id', $categories)
            ->where('active', 1)
            ->select('id', 'title', 'city_id', 'service_category_id', 'property_type_id', 'video')
            ->limit(10)
            ->get();

        // Get available cities, categories, and property types
        $availableCities = ServiceCategoryItem::whereIn('service_category_id', $categories)
            ->where('active', 1)
            ->whereNotNull('city_id')
            ->distinct()
            ->pluck('city_id');

        $availableCategories = ServiceCategoryItem::where('active', 1)
            ->whereNotNull('service_category_id')
            ->distinct()
            ->pluck('service_category_id');

        $availablePropertyTypes = ServiceCategoryItem::where('active', 1)
            ->whereNotNull('property_type_id')
            ->distinct()
            ->pluck('property_type_id');

        return response()->json([
            'success' => true,
            'debug_info' => [
                'requested_service_category_id' => $serviceCategoryId,
                'category_info' => $category,
                'categories_to_search' => $categories,
                'counts' => [
                    'total_items' => $totalItems,
                    'active_items' => $activeItems,
                    'items_with_video' => $itemsWithVideo,
                ],
                'sample_items' => $sampleItems,
                'available_filters' => [
                    'cities' => $availableCities,
                    'categories' => $availableCategories,
                    'property_types' => $availablePropertyTypes,
                ],
                'requested_filters' => [
                    'city_id' => $request->city_id,
                    'category_id' => $request->category_id,
                    'property_type_id' => $request->property_type_id,
                ]
            ]
        ]);
    }

    /**
     * Search items.
     *
     * @return \Illuminate\Http\Response
     */
    public function search(Request $request)
    {
        $request->validate([
            'keyword' => 'required|string',
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'service_category_id' => 'nullable|integer|exists:service_categories,id',
            'city_id' => 'nullable|integer|exists:cities,id',
            'sort' => 'nullable|string|in:asc,desc',
            'sort_by' => 'nullable|string|in:created_at,updated_at,title,price,views',
        ]);

        // Build the search query
        $query = ServiceCategoryItem::where('active', 1)
            ->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->keyword . '%')
                  ->orWhere('content', 'like', '%' . $request->keyword . '%');
            });

        // Apply category filter if provided
        if ($request->has('service_category_id')) {
            $category = ServiceCategory::find($request->service_category_id);
            if ($category) {
                $categories = [];
                if (!$category->parent) {
                    $categories = $category->childrenCategories->pluck('id')->toArray() ?? [];
                    array_push($categories, $category->id);
                } else {
                    array_push($categories, $category->id);
                }
                $query->whereIn('service_category_id', $categories);
            }
        }

        // Apply city filter if provided
        if ($request->has('city_id')) {
            $query->where('city_id', $request->city_id);
        }

        // Apply sorting
        $sortBy = $request->sort_by ?? 'created_at';
        $sortOrder = $request->sort ?? 'desc';
        $query->orderBy($sortBy, $sortOrder);

        // Paginate results
        $limit = $request->limit ?? 20;
        $list = $query->with(['gallery', 'facilities', 'user', 'city.country'])
            ->paginate($limit);

        // Log the search
        $search = ServiceCategoryItemSearch::create(['keyword' => $request->keyword]);

        if ($list->isNotEmpty()) {
            $search->update(['found' => true]);

            // Bulk increment views for all found items
            ServiceCategoryItem::whereIn('id', $list->pluck('id'))->increment('views');
        }

        return response()->json([
            'success' => !empty($list->items()),
            'message' => trans('api.' . (!empty($list->items()) ? 'data_retrieved_success' : 'no_data_found')),
            'data' => [
                'items' => ServiceCategoryItemResource::collection($list->items()),
                'pagination' => [
                    'current_page' => $list->currentPage(),
                    'last_page' => $list->lastPage(),
                    'per_page' => $list->perPage(),
                    'total' => $list->total(),
                    'has_more' => $list->hasMorePages(),
                ]
            ]
        ], !empty($list->items()) ? 200 : 404);
    }

    /**
     * Get a single item by ID.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $item = ServiceCategoryItem::with(['gallery', 'facilities', 'user', 'city.country'])
                ->where('active', 1)
                ->findOrFail($id);

            // Increment views for this item
            $item->increment('views');

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                new ServiceCategoryItemResource($item),
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.no_data_found'),
                null,
                404
            );
        }
    }

    /**
     * Create a new ServiceCategoryItem.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $user          = auth('api')->user();
        $validatedData = $request->validate([
            'title'                       => 'required|string|max:255',
            'content'                     => 'nullable|string',
            'service_category_id'         => 'required|exists:service_categories,id',
            'property_type_id'            => 'nullable|exists:property_types,id',
            'short_term_policy_id'        => 'nullable|exists:cancellation_policies,id',
            'long_term_policy_id'         => 'nullable|exists:cancellation_policies,id',
            'price'                       => 'nullable|numeric',
            'weekend_price'               => 'nullable|numeric',
            'week_price'                  => 'nullable|numeric',
            'month_price'                 => 'nullable|numeric',
            'lat'                         => 'nullable|numeric',
            'lon'                         => 'nullable|numeric',
            'address'                     => 'nullable|string',
            'no_guests'                   => 'nullable|integer',
            'beds'                        => 'nullable|integer',
            'baths'                       => 'nullable|integer',
            'booking_rules'               => 'nullable|string',
            'cancelation_rules'           => 'nullable|string',
            'confirmation'                => 'nullable|boolean',
            'facilities'                  => 'nullable|array',
            'facilities.*'                => 'exists:facilities,id',
            'include_commission_daily'    => 'nullable|boolean',
            'include_commission_weekly'   => 'nullable|boolean',
            'include_commission_monthly'  => 'nullable|boolean',
            'tourism_permit_number'       => 'required|string|min:5',
            'tourism_permit_document'     => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240', // 10MB max
            'image'                       => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'video'                       => 'nullable|mimes:mp4,mov,avi,wmv|max:51200', // 50MB max
            'gallery'                     => 'nullable|array',
            'gallery.*'                   => 'image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max per image
        ]);

        $validatedData['user_id'] = $user->id;
        $validatedData['active']  = 0;

        // Handle main image upload
        if ($request->hasFile('image')) {
            $validatedData['image'] = $request->file('image')->store('service_category_items', 'public');
        }

        // Handle video upload
        if ($request->hasFile('video')) {
            $validatedData['video'] = $request->file('video')->store('service_category_items', 'public');
        }

        // Handle tourism permit document upload
        if ($request->hasFile('tourism_permit_document')) {
            $validatedData['tourism_permit_document'] = $request->file('tourism_permit_document')->store('tourism_permits', 'public');
        }

        // Handle location-based city assignment
        if ($request->has('lat') && $request->has('lon')) {
            $lat = $validatedData['lat'];
            $lng = $validatedData['lon'];

            $radiusKm = 1000;

            // Get all cities with distance calculation
            $cities = City::selectRaw('*,
            (6371 * acos(cos(radians(?))
            * cos(radians(lat))
            * cos(radians(lon) - radians(?))
            + sin(radians(?))
            * sin(radians(lat)))) AS distance',
                [$lat, $lng, $lat]
            )->orderBy('distance')->get();

            // Determine current city (closest one)
            $currentCity = $cities->firstWhere('distance', '<=', $radiusKm);

            if ($currentCity) {
                $validatedData['city_id'] = $currentCity->id;
            }
        }

        $item = ServiceCategoryItem::create($validatedData);

        // Handle gallery images upload
        if ($request->hasFile('gallery')) {
            foreach ($request->file('gallery') as $image) {
                $item->gallery()->create([
                    'image' => $image->store('galleries', 'public'),
                ]);
            }
        }

        // Handle facilities relationship
        if ($request->has('facilities')) {
            $item->facilities()->sync($request->facilities);
        }

        // Load the item with relationships for the response
        $item->load(['gallery', 'facilities', 'user', 'city.country']);

        return response()->json([
            'success' => true,
            'message' => 'Item created successfully.',
            'data'    => new ServiceCategoryItemResource($item),
        ], 201);
    }

    public function update(Request $request, $id)
    {
        $item = ServiceCategoryItem::findOrFail($id);

        $validatedData = $request->validate([
            'title'                       => 'nullable|string|max:255',
            'content'                     => 'nullable|string',
            'service_category_id'         => 'nullable|exists:service_categories,id',
            'property_type_id'            => 'nullable|exists:property_types,id',
            'short_term_policy_id'        => 'nullable|exists:cancellation_policies,id',
            'long_term_policy_id'         => 'nullable|exists:cancellation_policies,id',
            'price'                       => 'nullable|numeric',
            'weekend_price'               => 'nullable|numeric',
            'week_price'                  => 'nullable|numeric',
            'month_price'                 => 'nullable|numeric',
            'weekly_price'                => 'nullable|numeric', // Alternative naming
            'monthly_price'               => 'nullable|numeric', // Alternative naming
            'lat'                         => 'nullable|numeric',
            'lon'                         => 'nullable|numeric',
            'address'                     => 'nullable|string',
            'no_guests'                   => 'nullable|integer',
            'beds'                        => 'nullable|integer',
            'baths'                       => 'nullable|integer',
            'booking_rules'               => 'nullable|string',
            'cancelation_rules'           => 'nullable|string',
            'tourism_permit'              => 'nullable|string|max:255',
            'confirmation'                => 'nullable|boolean',
            'requires_confirmation'       => 'nullable|boolean', // Alternative naming
            'facilities'                  => 'nullable|array',
            'facilities.*'                => 'exists:facilities,id',
            'include_commission_daily'    => 'nullable|boolean',
            'include_commission_weekly'   => 'nullable|boolean',
            'include_commission_monthly'  => 'nullable|boolean',
            'active'                      => 'nullable|boolean',
            'images_to_delete'            => 'nullable|array',
            'images_to_delete.*'          => 'string',
        ]);

        if ($request->has('lat') && $request->has('lon')) {
            $lat = $validatedData['lat'];
            $lng = $validatedData['lon'];

            $radiusKm = 1000;

            // Get all cities with distance calculation
            $cities = City::selectRaw('*,
            (6371 * acos(cos(radians(?))
            * cos(radians(lat))
            * cos(radians(lon) - radians(?))
            + sin(radians(?))
            * sin(radians(lat)))) AS distance',
                [$lat, $lng, $lat]
            )->orderBy('distance')->get();

            // Determine current city (closest one)
            $currentCity = $cities->firstWhere('distance', '<=', value: $radiusKm);

            if ($currentCity) {
                $validatedData['city_id'] = $currentCity->id;
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'No city found within the specified radius.',
                ], 404);
            }
        }

        // Handle alternative field names
        if (isset($validatedData['weekly_price'])) {
            $validatedData['week_price'] = $validatedData['weekly_price'];
            unset($validatedData['weekly_price']);
        }
        if (isset($validatedData['monthly_price'])) {
            $validatedData['month_price'] = $validatedData['monthly_price'];
            unset($validatedData['monthly_price']);
        }
        if (isset($validatedData['requires_confirmation'])) {
            $validatedData['confirmation'] = $validatedData['requires_confirmation'];
            unset($validatedData['requires_confirmation']);
        }

        // Remove fields that shouldn't be directly updated
        unset($validatedData['images_to_delete']);

        $item->update($validatedData);

        // Handle facilities sync
        if ($request->has('facilities')) {
            $item->facilities()->sync($request->facilities);
        }

        // Handle image deletion
        if ($request->has('images_to_delete') && is_array($request->images_to_delete)) {
            foreach ($request->images_to_delete as $imageUrl) {
                // Find and delete gallery images by URL
                $item->gallery()->where('image', 'like', '%' . basename($imageUrl))->delete();
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Service Category Item updated successfully.',
            'data'    => new ServiceCategoryItemResource($item),
        ]);
    }

    public function uploadGallery(Request $request, $id)
    {
        $item = ServiceCategoryItem::findOrFail($id);

        if ($request->hasFile('gallery')) {
            foreach ($request->file('gallery') as $image) {
                $item->gallery()->create([
                    'image' => $image->store('galleries', 'public'),
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => 'Images uploaded successfully.',
                'data'    => new ServiceCategoryItemResource($item),
            ]);
        }

        return response()->json(['message' => 'No images received.'], 422);
    }
}
