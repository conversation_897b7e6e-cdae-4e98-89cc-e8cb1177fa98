import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/feature/home/<USER>/views/place_details_screen.dart';
import 'package:gather_point/generated/l10n.dart';

class ReelsClassicView extends StatelessWidget {
  final List<Map<String, dynamic>> reelsData;
  final VoidCallback onRefresh;
  final DioConsumer dioConsumer;

  const ReelsClassicView({
    super.key,
    required this.reelsData,
    required this.onRefresh,
    required this.dioConsumer,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    if (reelsData.isEmpty) {
      return _buildEmptyState(context, s);
    }

    return RefreshIndicator(
      onRefresh: () async {
        HapticFeedback.mediumImpact();
        onRefresh();
      },
      color: AppColors.yellow,
      backgroundColor: Theme.of(context).cardColor,
      strokeWidth: 3,
      displacement: 60,
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 0.8,
              ),
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final reel = reelsData[index];
                  return ReelCard(
                    reel: reel,
                    index: index,
                    onTap: () => _navigateToDetails(context, reel),
                    onFavoriteToggle: () => _toggleFavorite(context, reel),
                  );
                },
                childCount: reelsData.length,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, S s) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 64,
            color: isDark ? Colors.white54 : Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            s.noReelsFound,
            style: AppTextStyles.font18Bold.copyWith(
              color: ThemeHelper.getPrimaryTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            s.tryDifferentSearch,
            style: AppTextStyles.font14Regular.copyWith(
              color: ThemeHelper.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _navigateToDetails(BuildContext context, Map<String, dynamic> reel) {
    // Convert reel data to PlaceDetailModel format for compatibility
    final placeDetail = PlaceDetailModel(
      id: reel['id'] ?? 0,
      title: reel['title'] ?? '',
      description: reel['description'] ?? '',
      price: _parsePrice(reel['price']),
      images: _parseImages(reel),
      videos: reel['video'] != null ? [reel['video']] : [],
      latitude: _parseDouble(reel['latitude']),
      longitude: _parseDouble(reel['longitude']),
      address: reel['address'] ?? '',
      rating: _parseDouble(reel['rating']),
      reviewsCount: reel['reviews_count'] ?? 0,
      favorite: reel['favorite'] ?? false,
      facilities: _parseFacilities(reel['facilities']),
      hostName: reel['host_name'] ?? '',
      hostImage: reel['host_image'] ?? '',
      hostRating: _parseDouble(reel['host_rating']),
      hostReviewsCount: reel['host_reviews_count'] ?? 0,
      cancellationPolicy: reel['cancellation_policy'] ?? '',
      checkInTime: reel['check_in_time'] ?? '',
      checkOutTime: reel['check_out_time'] ?? '',
      maxGuests: reel['max_guests'] ?? 1,
      bedrooms: reel['bedrooms'] ?? 0,
      bathrooms: reel['bathrooms'] ?? 0,
      area: _parseDouble(reel['area']),
      propertyType: reel['property_type'] ?? '',
      cityName: reel['city_name'] ?? '',
      districtName: reel['district_name'] ?? '',
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailsScreen(placeDetail: placeDetail),
      ),
    );
  }

  Future<void> _toggleFavorite(BuildContext context, Map<String, dynamic> reel) async {
    HapticFeedback.lightImpact();
    
    try {
      final response = await dioConsumer.post(
        '/api/client/favorites/toggle',
        data: {
          'item_id': reel['id'],
          'service_category_id': 2, // Reels category
        },
      );

      if (response['success'] == true) {
        // Update the reel's favorite status locally
        reel['favorite'] = !(reel['favorite'] ?? false);
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                reel['favorite'] 
                  ? S.of(context).addedToFavorites 
                  : S.of(context).removedFromFavorites,
              ),
              backgroundColor: AppColors.yellow,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update favorite: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper methods for data parsing
  double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is num) return price.toDouble();
    if (price is String) return double.tryParse(price) ?? 0.0;
    return 0.0;
  }

  double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is num) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  List<String> _parseImages(Map<String, dynamic> reel) {
    final images = <String>[];
    
    // Add main image if exists
    if (reel['image'] != null) {
      images.add(reel['image']);
    }
    
    // Add gallery images if exists
    if (reel['images'] is List) {
      for (final img in reel['images']) {
        if (img is String) images.add(img);
      }
    }
    
    return images;
  }

  List<Map<String, dynamic>> _parseFacilities(dynamic facilities) {
    if (facilities is List) {
      return facilities.map((f) => f is Map<String, dynamic> ? f : {}).toList();
    }
    return [];
  }
}

class ReelCard extends StatefulWidget {
  final Map<String, dynamic> reel;
  final VoidCallback onTap;
  final VoidCallback onFavoriteToggle;
  final int index;

  const ReelCard({
    super.key,
    required this.reel,
    required this.onTap,
    required this.onFavoriteToggle,
    this.index = 0,
  });

  @override
  State<ReelCard> createState() => _ReelCardState();
}

class _ReelCardState extends State<ReelCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start animation with delay based on index
    Future.delayed(Duration(milliseconds: widget.index * 100), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final s = S.of(context);
    final isRTL = context.read<LocaleCubit>().isArabic();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: GestureDetector(
              onTapDown: (_) => _onTapDown(),
              onTapUp: (_) => _onTapUp(),
              onTapCancel: () => _onTapCancel(),
              onTap: widget.onTap,
              child: AnimatedScale(
                scale: _isPressed ? 0.95 : 1.0,
                duration: const Duration(milliseconds: 100),
                child: Container(
                  decoration: BoxDecoration(
                    color: isDark ? AppColors.darkGrey : AppColors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: isDark 
                          ? Colors.black.withValues(alpha: 0.3)
                          : Colors.grey.withValues(alpha: 0.15),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(24),
                    child: Stack(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildImageSection(context),
                            _buildContentSection(context, s, theme),
                          ],
                        ),
                        _buildFavoriteButton(context, theme),
                        _buildRatingBadge(context, theme),
                        _buildVideoIndicator(context),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _onTapDown() {
    setState(() => _isPressed = true);
  }

  void _onTapUp() {
    setState(() => _isPressed = false);
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
  }

  Widget _buildImageSection(BuildContext context) {
    return Container(
      height: 120,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Stack(
        children: [
          // Main image
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
              color: Colors.grey[300],
            ),
            child: widget.reel['image'] != null
                ? ClipRRect(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
                    child: Image.network(
                      widget.reel['image'],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 40,
                          ),
                        );
                      },
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          color: Colors.grey[300],
                          child: const Center(
                            child: CircularProgressIndicator(
                              color: AppColors.yellow,
                              strokeWidth: 2,
                            ),
                          ),
                        );
                      },
                    ),
                  )
                : Container(
                    color: Colors.grey[300],
                    child: const Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                      size: 40,
                    ),
                  ),
          ),
          // Gradient overlay
          Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.1),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentSection(BuildContext context, S s, ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    final isRTL = context.read<LocaleCubit>().isArabic();

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              widget.reel['title'] ?? s.noTitle,
              style: AppTextStyles.font14SemiBold.copyWith(
                color: ThemeHelper.getPrimaryTextColor(context),
                height: 1.2,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),

            // Location
            if (widget.reel['city_name'] != null)
              Row(
                children: [
                  Icon(
                    Icons.location_on_outlined,
                    size: 12,
                    color: ThemeHelper.getSecondaryTextColor(context),
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: Text(
                      widget.reel['city_name'],
                      style: AppTextStyles.font12Regular.copyWith(
                        color: ThemeHelper.getSecondaryTextColor(context),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

            const Spacer(),

            // Price
            Row(
              children: [
                Expanded(
                  child: Text(
                    _formatPrice(widget.reel['price'], isRTL),
                    style: AppTextStyles.font14Bold.copyWith(
                      color: AppColors.yellow,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteButton(BuildContext context, ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    final isFavorite = widget.reel['favorite'] ?? false;

    return Positioned(
      top: 8,
      right: 8,
      child: GestureDetector(
        onTap: widget.onFavoriteToggle,
        child: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: isDark
              ? Colors.black.withValues(alpha: 0.6)
              : Colors.white.withValues(alpha: 0.9),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            isFavorite ? Icons.favorite : Icons.favorite_border,
            color: isFavorite ? Colors.red : Colors.grey,
            size: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildRatingBadge(BuildContext context, ThemeData theme) {
    final rating = widget.reel['rating'];
    if (rating == null || rating == 0) return const SizedBox.shrink();

    return Positioned(
      top: 8,
      left: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: AppColors.yellow,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.star,
              color: Colors.white,
              size: 10,
            ),
            const SizedBox(width: 2),
            Text(
              rating.toStringAsFixed(1),
              style: AppTextStyles.font10Bold.copyWith(
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoIndicator(BuildContext context) {
    return Positioned(
      bottom: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.6),
          borderRadius: BorderRadius.circular(6),
        ),
        child: const Icon(
          Icons.play_circle_outline,
          color: Colors.white,
          size: 16,
        ),
      ),
    );
  }

  String _formatPrice(dynamic price, bool isRTL) {
    final parsedPrice = _parsePrice(price);
    if (parsedPrice == 0) return isRTL ? 'غير محدد' : 'Price not set';

    final currency = isRTL ? 'ر.س' : 'SR';
    final formattedPrice = parsedPrice.toStringAsFixed(0);

    return isRTL
      ? '$formattedPrice $currency'
      : '$currency $formattedPrice';
  }

  double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is num) return price.toDouble();
    if (price is String) return double.tryParse(price) ?? 0.0;
    return 0.0;
  }
}
