import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/host/data/models/property_metadata_models.dart';
import 'dart:developer' as developer;

part 'reels_state.dart';

class ReelsCubit extends Cubit<ReelsState> {
  final DioConsumer _dioConsumer;
  
  // Pagination
  int currentPage = 1;
  bool hasMore = true;
  List<Map<String, dynamic>> allReels = [];
  
  ReelsCubit(this._dioConsumer) : super(const ReelsInitial());

  /// Safe emit to prevent emitting after cubit is closed
  void _safeEmit(ReelsState state) {
    if (!isClosed) {
      emit(state);
    }
  }

  /// Initialize reels with optional filters
  Future<void> initializeReels({
    int serviceCategoryId = 2,
    City? selectedCity,
    int? categoryId,
    int? propertyTypeId,
  }) async {
    _safeEmit(const ReelsLoading());

    currentPage = 1;
    hasMore = true;
    allReels.clear();

    // First load categories and property types
    await loadFilterMetadata(selectedCity?.id);

    // Then fetch reels
    await _fetchReels(
      serviceCategoryId: serviceCategoryId,
      selectedCity: selectedCity,
      categoryId: categoryId,
      propertyTypeId: propertyTypeId,
    );
  }

  /// Fetch reels with filters
  Future<void> _fetchReels({
    required int serviceCategoryId,
    City? selectedCity,
    int? categoryId,
    int? propertyTypeId,
    bool loadMore = false,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'service_category_id': serviceCategoryId,
        'reels': 1,
        // 'has_video': 1, // Removed - too restrictive
        'page': currentPage,
        'limit': 10,
      };

      // Add filters with debug logging
      if (selectedCity != null) {
        queryParams['city_id'] = selectedCity.id;
        developer.log('Added city filter: ${selectedCity.id}', name: 'ReelsCubit');
      }

      if (categoryId != null) {
        queryParams['category_id'] = categoryId;
        developer.log('Added category filter: $categoryId', name: 'ReelsCubit');
      }

      if (propertyTypeId != null) {
        queryParams['property_type_id'] = propertyTypeId;
        developer.log('Added property type filter: $propertyTypeId', name: 'ReelsCubit');
      }

      developer.log('Final query params: $queryParams', name: 'ReelsCubit');

      final response = await _dioConsumer.get(
        '/api/items/list',
        queryParameters: queryParams,
      );

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final List<Map<String, dynamic>> newReels =
            List<Map<String, dynamic>>.from(data['items'] ?? []);
        
        final pagination = data['pagination'] as Map<String, dynamic>?;
        hasMore = pagination?['has_more'] ?? false;

        if (loadMore && currentPage > 1) {
          // Append new reels to existing ones
          allReels.addAll(newReels);
        } else {
          // Replace reels (first page or refresh)
          allReels = newReels;
        }

        developer.log('Fetched ${newReels.length} reels, total: ${allReels.length}', name: 'ReelsCubit');

        _safeEmit(ReelsLoaded(
          reels: List.from(allReels),
          filteredReels: List.from(allReels),
          hasMore: hasMore,
          isLoadingMore: false,
          categories: const [],
          propertyTypes: const [],
          selectedCity: selectedCity,
          selectedCategoryId: categoryId,
          selectedPropertyTypeId: propertyTypeId,
        ));
      } else {
        _safeEmit(const ReelsError('Failed to load reels'));
      }
    } catch (e) {
      developer.log('Failed to load reels: $e', name: 'ReelsCubit');
      _safeEmit(ReelsError('Failed to load reels: ${e.toString()}'));
    }
  }

  /// Load more reels for pagination
  Future<void> loadMoreReels({
    int serviceCategoryId = 2,
    City? selectedCity,
    int? categoryId,
    int? propertyTypeId,
  }) async {
    final currentState = state;
    if (currentState is ReelsLoaded && hasMore && !currentState.isLoadingMore) {
      _safeEmit(currentState.copyWith(isLoadingMore: true));
      
      currentPage++;
      await _fetchReels(
        serviceCategoryId: serviceCategoryId,
        selectedCity: selectedCity,
        categoryId: categoryId,
        propertyTypeId: propertyTypeId,
        loadMore: true,
      );
    }
  }

  /// Load filter metadata (categories and property types)
  Future<void> loadFilterMetadata(int? cityId) async {
    try {
      developer.log('Loading filter metadata...', name: 'ReelsCubit');

      // Load both categories and property types
      final results = await Future.wait([
        _fetchCategoriesData(cityId),
        _fetchPropertyTypesData(),
      ]);

      final categoriesData = results[0];
      final propertyTypesData = results[1];

      developer.log('Loaded ${categoriesData.length} categories and ${propertyTypesData.length} property types', name: 'ReelsCubit');

      // Update state with both categories and property types
      final currentState = state;
      if (currentState is ReelsLoaded) {
        developer.log('Updating existing ReelsLoaded state with metadata', name: 'ReelsCubit');
        _safeEmit(currentState.copyWith(
          categories: categoriesData,
          propertyTypes: propertyTypesData,
        ));
      } else {
        // Create initial state with metadata
        developer.log('Creating new ReelsLoaded state with metadata', name: 'ReelsCubit');
        _safeEmit(ReelsLoaded(
          reels: const [],
          filteredReels: const [],
          hasMore: false,
          isLoadingMore: false,
          categories: categoriesData,
          propertyTypes: propertyTypesData,
          selectedCity: null,
          selectedCategoryId: null,
          selectedPropertyTypeId: null,
        ));
      }

      // Force emit again to ensure UI updates
      final newState = state;
      if (newState is ReelsLoaded) {
        developer.log('Final state check - Categories: ${newState.categories.length}, Property Types: ${newState.propertyTypes.length}', name: 'ReelsCubit');
      }

      developer.log('Filter metadata loading completed', name: 'ReelsCubit');
    } catch (e) {
      developer.log('Error loading filter metadata: $e', name: 'ReelsCubit');
    }
  }

  /// Fetch categories data without emitting state
  Future<List<Map<String, dynamic>>> _fetchCategoriesData(int? cityId) async {
    try {
      developer.log('Fetching categories data with cityId: $cityId', name: 'ReelsCubit');

      // Get subcategories by passing parent id = 1
      final response = await _dioConsumer.get(
        '/api/service_categories/list',
        queryParameters: {'service_category_id': 1}, // Get subcategories of parent category 1
      );

      developer.log('Categories API response: $response', name: 'ReelsCubit');

      if ((response['success'] == true || response['status'] == true) && response['data'] != null) {
        // The response data is directly an array
        final List<Map<String, dynamic>> categoriesData =
            List<Map<String, dynamic>>.from(response['data'] ?? []);

        developer.log('Categories data loaded: ${categoriesData.length} items', name: 'ReelsCubit');

        // Log first few items to see structure
        if (categoriesData.isNotEmpty) {
          developer.log('First category structure: ${categoriesData.first}', name: 'ReelsCubit');
        }

        return categoriesData;
      }
    } catch (e) {
      developer.log('Failed to fetch categories data: $e', name: 'ReelsCubit');
      // Return mock data for testing
      return _getMockCategories();
    }
    return [];
  }

  /// Fetch property types data without emitting state
  Future<List<Map<String, dynamic>>> _fetchPropertyTypesData() async {
    try {
      developer.log('Fetching property types data...', name: 'ReelsCubit');

      final response = await _dioConsumer.get('/api/property_types/list');

      developer.log('Property types API response success: ${response['success']}', name: 'ReelsCubit');

      if (response['success'] == true && response['data'] != null) {
        final List<Map<String, dynamic>> propertyTypesData =
            List<Map<String, dynamic>>.from(response['data'] ?? []);

        developer.log('Property types data loaded: ${propertyTypesData.length} items', name: 'ReelsCubit');
        return propertyTypesData;
      }
    } catch (e) {
      developer.log('Failed to fetch property types data: $e', name: 'ReelsCubit');
      // Return mock data for testing
      return _getMockPropertyTypes();
    }
    return [];
  }

  /// Mock categories for testing when API fails
  List<Map<String, dynamic>> _getMockCategories() {
    return [
      {'id': 2, 'title': 'شاليهات', 'icon': 'home', 'order': 1, 'parent': 1},
      {'id': 3, 'title': 'استراحات', 'icon': 'restaurant', 'order': 2, 'parent': 1},
      {'id': 4, 'title': 'مخيمات', 'icon': 'event', 'order': 3, 'parent': 1},
      {'id': 5, 'title': 'فنادق', 'icon': 'entertainment', 'order': 4, 'parent': 1},
    ];
  }

  /// Mock property types for testing when API fails
  List<Map<String, dynamic>> _getMockPropertyTypes() {
    return [
      {'id': 1, 'title': 'Apartment', 'order': 1},
      {'id': 2, 'title': 'Villa', 'order': 2},
      {'id': 3, 'title': 'Chalet', 'order': 3},
    ];
  }

  /// Update selected category
  void updateSelectedCategory(int? categoryId) {
    final currentState = state;
    if (currentState is ReelsLoaded) {
      _safeEmit(currentState.copyWith(selectedCategoryId: categoryId));
    }
  }

  /// Update selected property type
  void updateSelectedPropertyType(int? propertyTypeId) {
    final currentState = state;
    if (currentState is ReelsLoaded) {
      _safeEmit(currentState.copyWith(selectedPropertyTypeId: propertyTypeId));
    }
  }



  /// Search reels
  Future<void> searchReels({
    required String searchQuery,
    int serviceCategoryId = 2,
    City? selectedCity,
    int? categoryId,
    int? propertyTypeId,
  }) async {
    if (searchQuery.trim().isEmpty) {
      // If search is empty, refresh with current filters
      await refreshReels(
        serviceCategoryId: serviceCategoryId,
        selectedCity: selectedCity,
        categoryId: categoryId,
        propertyTypeId: propertyTypeId,
      );
      return;
    }

    _safeEmit(const ReelsLoading());
    
    currentPage = 1;
    hasMore = true;
    allReels.clear();

    try {
      final queryParams = <String, dynamic>{
        'service_category_id': serviceCategoryId,
        'keyword': searchQuery.trim(),
        'reels': 1,
        'has_video': 1, // Only return items with videos
        'page': currentPage,
        'limit': 10,
      };

      // Add filters
      if (selectedCity != null) {
        queryParams['city_id'] = selectedCity.id;
      }
      
      if (categoryId != null) {
        queryParams['category_id'] = categoryId;
      }
      
      if (propertyTypeId != null) {
        queryParams['property_type_id'] = propertyTypeId;
      }

      final response = await _dioConsumer.get(
        '/api/items/search',
        queryParameters: queryParams,
      );

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final List<Map<String, dynamic>> searchResults =
            List<Map<String, dynamic>>.from(data['items'] ?? []);
        
        final pagination = data['pagination'] as Map<String, dynamic>?;
        hasMore = pagination?['has_more'] ?? false;

        allReels = searchResults;

        _safeEmit(ReelsLoaded(
          reels: List.from(allReels),
          filteredReels: List.from(allReels),
          hasMore: hasMore,
          isLoadingMore: false,
          categories: const [],
          propertyTypes: const [],
          selectedCity: selectedCity,
          selectedCategoryId: categoryId,
          selectedPropertyTypeId: propertyTypeId,
          searchQuery: searchQuery,
        ));
      } else {
        _safeEmit(const ReelsError('No search results found'));
      }
    } catch (e) {
      developer.log('Search failed: $e', name: 'ReelsCubit');
      _safeEmit(ReelsError('Search failed: ${e.toString()}'));
    }
  }

  /// Refresh reels
  Future<void> refreshReels({
    int serviceCategoryId = 2,
    City? selectedCity,
    int? categoryId,
    int? propertyTypeId,
  }) async {
    currentPage = 1;
    hasMore = true;
    allReels.clear();
    
    await _fetchReels(
      serviceCategoryId: serviceCategoryId,
      selectedCity: selectedCity,
      categoryId: categoryId,
      propertyTypeId: propertyTypeId,
    );
  }

  /// Apply filters to current reels
  void applyFilters({
    String? searchQuery,
    int? categoryId,
    int? propertyTypeId,
    String? sortBy,
  }) {
    final currentState = state;
    if (currentState is! ReelsLoaded) return;

    List<Map<String, dynamic>> filtered = List.from(allReels);

    // Apply search filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filtered = filtered.where((reel) {
        final title = ((reel['title'] as String?) ?? '').toLowerCase();
        final description = ((reel['description'] as String?) ?? '').toLowerCase();
        final location = ((reel['location'] as String?) ?? '').toLowerCase();

        return title.contains(query) ||
               description.contains(query) ||
               location.contains(query);
      }).toList();
    }

    // Apply category filter
    if (categoryId != null && categoryId > 0) {
      filtered = filtered.where((reel) {
        final reelCategoryId = reel['category_id'] ?? reel['service_category_id'];
        return reelCategoryId == categoryId;
      }).toList();
    }

    // Apply property type filter
    if (propertyTypeId != null && propertyTypeId > 0) {
      filtered = filtered.where((reel) {
        final reelPropertyTypeId = reel['property_type_id'];
        return reelPropertyTypeId == propertyTypeId;
      }).toList();
    }

    // Apply sorting
    if (sortBy != null) {
      _applySorting(filtered, sortBy);
    }

    _safeEmit(currentState.copyWith(
      filteredReels: filtered,
      selectedCategoryId: categoryId,
      selectedPropertyTypeId: propertyTypeId,
      searchQuery: searchQuery,
    ));
  }

  /// Apply sorting to reels
  void _applySorting(List<Map<String, dynamic>> reels, String sortBy) {
    switch (sortBy) {
      case 'newest':
        reels.sort((a, b) {
          final dateA = DateTime.tryParse(a['created_at'] ?? '') ?? DateTime.now();
          final dateB = DateTime.tryParse(b['created_at'] ?? '') ?? DateTime.now();
          return dateB.compareTo(dateA);
        });
        break;
      case 'oldest':
        reels.sort((a, b) {
          final dateA = DateTime.tryParse(a['created_at'] ?? '') ?? DateTime.now();
          final dateB = DateTime.tryParse(b['created_at'] ?? '') ?? DateTime.now();
          return dateA.compareTo(dateB);
        });
        break;
      case 'price_low':
        reels.sort((a, b) {
          final priceA = _parsePrice(a['price']);
          final priceB = _parsePrice(b['price']);
          return priceA.compareTo(priceB);
        });
        break;
      case 'price_high':
        reels.sort((a, b) {
          final priceA = _parsePrice(a['price']);
          final priceB = _parsePrice(b['price']);
          return priceB.compareTo(priceA);
        });
        break;
      case 'most_liked':
        reels.sort((a, b) {
          final likesA = _parseInt(a['likes_count'] ?? a['favorite_count'] ?? 0);
          final likesB = _parseInt(b['likes_count'] ?? b['favorite_count'] ?? 0);
          return likesB.compareTo(likesA);
        });
        break;
    }
  }

  /// Helper methods
  double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is num) return price.toDouble();
    if (price is String) return double.tryParse(price) ?? 0.0;
    return 0.0;
  }

  int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  /// Toggle favorite
  Future<void> toggleFavorite(Map<String, dynamic> reel) async {
    try {
      final response = await _dioConsumer.post(
        '/api/client/favorites/toggle',
        data: {
          'item_id': reel['id'],
          'service_category_id': 2,
        },
      );

      if (response['success'] == true) {
        // Update the reel's favorite status locally
        reel['favorite'] = !(reel['favorite'] ?? false);
        
        final currentState = state;
        if (currentState is ReelsLoaded) {
          // Update both allReels and filteredReels
          final updatedAllReels = allReels.map((r) {
            if (r['id'] == reel['id']) {
              return Map<String, dynamic>.from(r)..['favorite'] = reel['favorite'];
            }
            return r;
          }).toList();
          
          final updatedFilteredReels = currentState.filteredReels.map((r) {
            if (r['id'] == reel['id']) {
              return Map<String, dynamic>.from(r)..['favorite'] = reel['favorite'];
            }
            return r;
          }).toList();

          allReels = updatedAllReels;
          _safeEmit(currentState.copyWith(
            reels: updatedAllReels,
            filteredReels: updatedFilteredReels,
          ));
        }
      }
    } catch (e) {
      developer.log('Failed to toggle favorite: $e', name: 'ReelsCubit');
      rethrow;
    }
  }

  @override
  Future<void> close() {
    developer.log('ReelsCubit closed', name: 'ReelsCubit');
    return super.close();
  }
}
